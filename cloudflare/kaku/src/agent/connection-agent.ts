import { CDP } from '../browser/simple-cdp';
import { Agent, type Connection, type ConnectionContext, type WSMessage } from 'agents';
import { html } from 'hono/html';
import {
  refreshTensorFlowBoundingBox,
  startCaptchaMonitoring,
  stopCaptchaMonitoring,
} from '../browser';
import { Environment } from '../common/types';
import { encryptData, getEncryptionKey } from '../common/utils';
import {
  ErrorService,
  ErrorDisplay,
  ErrorContext,
  ErrorCollector,
  ErrorRouter,
} from '../common/error';
import { templateEngine } from '../ui';
import { BrowserServiceFactory, RemoteBrowserService } from '../workflow/services';
import { PlatformTypes } from '../ui/constants';
import { AgentState, ExtractResult } from './types';
import { CaptchaBoundingBox } from './types/extract-result';
import { BoundingRect } from './types/agent-state';

export class Connections extends Agent<Environment, AgentState> {
  initialState: AgentState = {
    status: 'initial',
    captchaSetupComplete: false,
  };
  offererConn: Connection | null = null;
  answererConn: Connection | null = null;
  cdpClient: CDP | null = null;
  targetSessionId: string | null = null;
  private cdpErrorHandlers: (() => void) | null = null;
  private browserService: RemoteBrowserService = BrowserServiceFactory.createFromEnvironment(
    this.env,
  );

  async onFormStateChange(result: ExtractResult) {
    let newStatus: AgentState['status'] =
      result.extractedData.pageType === 'authenticated' ? 'completed' : 'waiting-for-human';

    const newState: AgentState = {
      ...this.state,
      status: newStatus,
      page: result.extractedData,
      history: this.state?.history
        ? [...this.state.history, result.extractedData]
        : [result.extractedData],
    };

    this.setState(newState);
  }

  onStateUpdate(state: AgentState | undefined, source: Connection | 'server'): void {
    if (!state) return;
    console.log('State updated:', state.status);

    const uiUpdate = html` <div id="connection-flow" hx-swap-oob="innerHTML">
      ${templateEngine.generateContent(state, this.name.split(':').pop() as PlatformTypes)}
    </div>`;

    this.broadcast(uiUpdate as string);
  }

  arrayBufferToBase64 = (buffer: ArrayBuffer) => Buffer.from(buffer).toString('base64');

  onConnect(connection: Connection, ctx: ConnectionContext): void {
    console.log(`Connected: to ${this.name}`, connection.id);
    const connections = this.getConnections();
    for (const connection of connections) {
      console.log(connection.id);
    }
    // State of interactivity for the end user. Can be paused or enabled.
    if (this.state.status === 'waiting-for-human' && this.state.interactivity) {
      console.log('Enabling interactivity');
      this.broadcast(
        JSON.stringify({
          type: 'interactivity-status',
          status: this.state.interactivity.status,
          cropBox: this.state.interactivity.cropBox,
          inputBoxRects: this.state.interactivity.inputBoxRects,
        }),
      );
    }

    console.log('generating content');
    const uiUpdate = html` <div id="connection-flow" hx-swap-oob="innerHTML">
      ${templateEngine.generateContent(this.state, this.name.split(':').pop() as PlatformTypes)}
    </div>`;
    this.broadcast(uiUpdate as string);
  }

  onClose(connection: Connection): void | Promise<void> {
    console.log(`Disconnected from ${this.name}`, connection.id);

    this.cleanupCDPErrorMonitoring();
  }

  override async onMessage(connection: Connection, message: WSMessage) {
    if (typeof message !== 'string') {
      console.error('Invalid message type received:', typeof message);
      return;
    }
    try {
      const data = JSON.parse(message);
      switch (data.type) {
        case 'agree_and_continue': {
          console.log('Received agree_and_continue');
          await this.handleFlowInitiate({
            platform: this.name.split(':').pop() as 'facebook' | 'github' | 'test',
          });
          break;
        }
        case 'cropbox-update': {
          console.log('Received cropbox-update');
          await this.handleCropBoxUpdate(data.cropBox as CaptchaBoundingBox, data.inputBoxRects);
          break;
        }
        case 'retry': {
          console.log('Received retry');
          await this.handleRetry();
          break;
        }
        case 'input-event': {
          console.log('Received input-event');
          // Forward input events to all connections (screen-cropper will handle it)
          this.broadcast(JSON.stringify({ type: 'input-event', data: data.data }));
          break;
        }
        case 'request-frame': {
          console.log('Received request-frame');
          // Forward frame requests to all connections (screen-cropper will handle it)
          this.broadcast(JSON.stringify({ type: 'request-frame' }));
          break;
        }
        default:
          this.broadcast(JSON.stringify({ ...data }), [connection.id]);
      }
      if (data.HEADERS && data.HEADERS['HX-Request'] === 'true') {
        await this.handleInputSubmitted(connection, data);
        return;
      }
    } catch (error) {
      console.error(error);
      throw Error(`An error occurred processing ws message ${message}, ${JSON.stringify(error)}`);
    }
  }

  async deleteAll() {
    await this.ctx.storage.deleteAll();
  }

  async onAttachedToTarget({ params }: { params: any }) {
    // get session ID
    const { sessionId, targetInfo } = params;
    console.log('Target attached:', { sessionId, targetInfo });

    if (targetInfo.type === 'page') {
      this.targetSessionId = sessionId;
      await this.cdpClient!.Page.enable(undefined, sessionId);
      await this.cdpClient!.Runtime.enable(undefined, sessionId);
    }
  }

  injectMouseTracker = async () => {
    if (!this.cdpClient) {
      console.error('CDP client not initialized');
      return;
    }

    await this.cdpClient.Runtime.evaluate({
      expression: `
      (function () {
        console.log('[KAZEEL] Injecting mouse tracker');
        document.addEventListener('mousemove', (event) => {
          const marker = document.createElement('div');
          Object.assign(marker.style, {
            position: 'absolute',
            left: event.clientX + 'px',
            top: event.clientY + 'px',
            width: '6px',
            height: '6px',
            borderRadius: '50%',
            backgroundColor: 'rgba(255, 0, 0, 0.4)',
            zIndex: '999999',
            pointerEvents: 'none',
            transform: 'translate(-50%, -50%)',
          });
          document.body.appendChild(marker);
          setTimeout(() => marker.remove(), 500);
        });
      })();
    `,
      awaitPromise: true,
    });
  };

  async handleFlowInitiate(config: { platform: PlatformTypes }) {
    console.log(`handling flow initiate for ${config.platform} with status ${this.state.status}`);
    if (this.state.status !== 'initial') return;
    this.setState({
      ...this.state,
      status: 'waiting-for-agent',
    });

    const userId = this.name.split(':')[0];

    try {
      // Use the browser service to create a new session
      const browserSession = await this.browserService.createSession({
        browserArgs: ['--auto-accept-this-tab-capture'],
        device: ['desktop'],
        solveCaptchas: false,
      });
      const wsEndpoint = browserSession.wsEndpoint;

      this.cdpClient = new CDP({ webSocketDebuggerUrl: wsEndpoint });
      this.setupCDPErrorMonitoring();

      await this.cdpClient.Target.setAutoAttach({
        autoAttach: true,
        flatten: true,
        waitForDebuggerOnStart: false,
      });
      // Add event listener triggered when a session is attached to a target
      this.cdpClient.Target.addEventListener(
        'attachedToTarget',
        this.onAttachedToTarget.bind(this),
      );

      const instance = await this.env.CONNECTIONS_WORKFLOW.create({
        params: { platformId: config.platform, userId, sessionId: browserSession.sessionId! },
      });

      this.setState({
        ...this.state,
        workflowId: instance.id,
        sessionId: browserSession.sessionId,
      });

      const uiUpdate = html` <div id="connection-flow" hx-swap-oob="innerHTML">
        ${templateEngine.generateContent(this.state, this.name.split(':').pop() as PlatformTypes)}
      </div>`;
      this.broadcast(uiUpdate as string);
    } catch (error) {
      await ErrorService.handleGenericError(
        'agent',
        'SYSTEM_ERROR',
        error,
        {
          sessionId: 'not_initialized',
          userId: userId,
          platformId: config.platform,
        },
        this.env,
        'error',
      );
    }
  }

  async handleAndDisplayError(errorContext: ErrorContext): Promise<void> {
    const processedError = ErrorService.processErrorForUI(errorContext);

    this.setState({
      ...this.state,
      status: 'error',
      errorMessage: processedError.userMessage,
    });

    const errorUI = html` <div id="connection-flow" hx-swap-oob="innerHTML">
      ${ErrorDisplay(processedError)}
    </div>`;

    this.broadcast(errorUI as string);
  }

  private async handleInputSubmitted(_: Connection, payload: FormSubmissionPayload) {
    if (this.state.status !== 'waiting-for-human') return;
    if (!this.state.workflowId) throw Error('workflowID should not be null');
    this.setState({ ...this.state, status: 'waiting-for-agent' });
    const { HEADERS, ...formValues } = payload;
    const workflowId = this.state.workflowId;
    const currentActions = this.state.page?.actions || [];

    // Map the form values to the existing actions
    const actions = currentActions.map((action) => {
      if (action.type === 'fill' && action.name) {
        const value = formValues[action.name];
        if (value) {
          return {
            ...action,
            value,
          };
        }
      }
      return action;
    });

    console.log('Mapped actions:', actions);
    const encryptionKey = await getEncryptionKey();
    const encrypted = await encryptData(JSON.stringify({ actions }), encryptionKey);

    const workflow = await this.env.CONNECTIONS_WORKFLOW.get(workflowId);
    await workflow.sendEvent({
      type: 'form-submission',
      payload: encrypted,
    });

    const uiUpdate = html` <div id="connection-flow" hx-swap-oob="innerHTML">
      ${templateEngine.generateContent(this.state, this.name.split(':').pop() as PlatformTypes)}
    </div>`;

    // Send a loading state update via HTMX
    this.broadcast(uiUpdate as string);
  }

  async handleCaptchaSolvedEvent(differenceData: {
    differencePercentage: number;
    timestamp: string;
    executionContextId: number;
  }) {
    console.log(
      `Captcha solved event received: ${differenceData.differencePercentage.toFixed(2)}%`,
    );
    if (!this.state.workflowId) throw Error('workflowID should not be null');

    try {
      // Send captcha solved notification to workflow
      const workflowId = this.state.workflowId;
      const workflow = await this.env.CONNECTIONS_WORKFLOW.get(workflowId);
      await workflow.sendEvent({
        type: 'captcha-solved',
        payload: differenceData,
      });
    } catch (error) {
      console.error('Failed to handle captcha solved event:', error);
    }
  }

  async pauseInteractivity() {
    this.setState({
      ...this.state,
      interactivity: {
        ...this.state.interactivity!,
        status: 'paused',
      },
    });
  }

  async resumeInteractivity() {
    this.setState({
      ...this.state,
      interactivity: {
        ...this.state.interactivity!,
        status: 'enabled',
      },
    });
  }

  async handleCropBoxUpdate(newCropBox: CaptchaBoundingBox, inputBoxRects: BoundingRect[]) {
    this.setState({
      ...this.state,
      interactivity: {
        status: 'enabled',
        cropBox: newCropBox,
        inputBoxRects,
      },
    });
    this.broadcast(
      JSON.stringify({
        type: 'interactivity-status',
        status: 'enabled',
        cropBox: newCropBox,
        inputBoxRects,
      }),
    );
  }

  async handleCaptchaDetected(
    executionContextId: number,
    viewport: { width: number; height: number },
  ) {
    if (!this.cdpClient) {
      console.error('Cannot handle captcha detected: cdpClient not initialized');
      return;
    }

    if (this.state.status === 'error') {
      console.log('Agent is in error state, skipping captcha detection setup');
      throw new Error('Agent is in error state - cannot proceed with captcha detection');
    }

    if (this.state.captchaSetupComplete) {
      console.log(
        'Captcha setup already complete, skipping re-initialization. Refreshing bounding box',
      );
      await refreshTensorFlowBoundingBox(
        this.cdpClient,
        executionContextId,
        viewport,
        this.targetSessionId!,
      );
      return;
    }

    console.log('Captcha detected, setting up bindings for screenshots/difference and monitoring');

    const bindingListener = async (event: any) => {
      if (event.name === '__captchaSolved__') {
        try {
          const payload = JSON.parse(event.payload);
          if (payload.type === 'CAPTCHA_SOLVED') {
            console.log(`Received CAPTCHA_SOLVED via Binding`);
            // Call the internal handler method
            await this.handleCaptchaSolvedEvent({
              differencePercentage: payload.differencePercentage,
              timestamp: payload.timestamp,
              executionContextId,
            });
          } else {
            console.warn(
              'Received unexpected payload type on __captchaSolved__ binding:',
              payload.type,
            );
          }
        } catch (parseError) {
          console.error(
            'Failed to parse payload from __captchaSolved__ binding:',
            event.payload,
            parseError,
          );
        }
        return;
      }

      console.warn(`Received unhandled binding call: ${event.name}`);
    };

    try {
      await this.cdpClient.Runtime.addBinding(
        {
          name: '__captchaSolved__',
          executionContextId,
        },
        this.targetSessionId!,
      );
      console.log('Added Runtime binding for captcha solved notifications.');

      this.cdpClient.Runtime.removeEventListener('bindingCalled', bindingListener);
      this.cdpClient.Runtime.addEventListener('bindingCalled', bindingListener);
      console.log('Attached listener for Runtime.bindingCalled');
    } catch (bindingError) {
      console.error('Failed to set up one or more Runtime bindings:', bindingError);
      return;
    }

    this.setState({
      ...this.state,
      status: 'waiting-for-human',
      interactivity: {
        ...this.state.interactivity!,
        status: 'enabled',
      },
      captchaSetupComplete: true,
    });

    await startCaptchaMonitoring(
      this.cdpClient,
      {
        diffThreshold: 5,
        screenshotQuality: 90,
      },
      executionContextId,
      this.targetSessionId!,
    );
  }

  async handleCaptchaSolved() {
    console.log('Captcha solved, updating UI');

    this.setState({
      ...this.state,
      status: 'waiting-for-agent',
      interactivity: {
        ...this.state.interactivity!,
        status: 'completed',
      },
    });

    if (this.cdpClient) {
      await stopCaptchaMonitoring(this.cdpClient, this.targetSessionId!);
    }

    const uiUpdate = html` <div id="connection-flow" hx-swap-oob="innerHTML">
      ${templateEngine.generateContent(this.state, this.name.split(':').pop() as PlatformTypes)}
    </div>`;
    this.broadcast(JSON.stringify({ type: 'interactivity-status', status: 'completed' }));
    this.broadcast(uiUpdate as string);
  }

  private setupCDPErrorMonitoring(): void {
    if (!this.cdpClient) return;

    const userId = this.name.split(':')[0];
    const platformId = this.name.split(':')[1];

    const handleRuntimeException = (event: any) => {
      const exceptionDetails = event.params?.exceptionDetails;

      console.log(`[CDP] [DO: ${this.name}] Runtime exception:`, JSON.stringify(event, null, 2));

      const errorContext = ErrorCollector.collectError(
        'cdp',
        'SCRIPT_INJECTION_FAILED',
        event,
        'error',
        {
          userId,
          platformId,
          sessionId: this.state.sessionId || 'unknown',
        },
      );

      const classifiedError = ErrorRouter.classifyError(errorContext);
      this.handleAndDisplayError(classifiedError);
    };

    const handleConsoleAPI = (event: any) => {
      // Extract console API details from event.params (simple-cdp event structure)
      const consoleType = event.params?.type;

      if (consoleType === 'error') {
        console.log(
          `[CDP] [DO: ${this.name}] Console error detected:`,
          JSON.stringify(event, null, 2),
        );

        const errorContext = ErrorCollector.collectError(
          'cdp',
          'SCRIPT_INJECTION_FAILED',
          event,
          'error',
          {
            userId,
            platformId,
            sessionId: this.state.sessionId || 'unknown',
          },
        );

        const classifiedError = ErrorRouter.classifyError(errorContext);
        this.handleAndDisplayError(classifiedError);
      }
    };

    // Set up CDP event listeners
    this.cdpClient.Runtime.addEventListener('exceptionThrown', handleRuntimeException);
    this.cdpClient.Runtime.addEventListener('consoleAPICalled', handleConsoleAPI);

    this.cdpErrorHandlers = () => {
      if (this.cdpClient) {
        this.cdpClient.Runtime.removeEventListener('exceptionThrown', handleRuntimeException);
        this.cdpClient.Runtime.removeEventListener('consoleAPICalled', handleConsoleAPI);
      }
    };

    console.log(`[Agent] [DO: ${this.name}] CDP error monitoring enabled`);
  }

  private cleanupCDPErrorMonitoring(): void {
    console.log(`[Agent] [DO: ${this.name}] CDP error monitoring disabled`);
    if (this.cdpErrorHandlers) {
      this.cdpErrorHandlers();
      this.cdpErrorHandlers = null;
    }
  }

  async handleRetry(): Promise<void> {
    this.cleanupCDPErrorMonitoring();

    this.setState(this.initialState);
  }
}

type FormSubmissionPayload = Record<string, string>;
