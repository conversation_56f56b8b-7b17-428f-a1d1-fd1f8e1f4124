import Protocol from 'devtools-protocol';
import { CDP } from '../browser/simple-cdp';

declare global {
  interface Window {
    controlTab: any;
  }
}

/**
 * Control Tab - Isolated CDP Controller
 *
 * This script runs in a separate browser tab and manages all CDP operations
 * for the target tab. It receives commands via message passing and executes
 * CDP operations, providing isolation from target page reloads.
 */
(function () {
  const config = {
    debug: true,
  };

  let cdpClient: CDP | null = null;
  let sessionId: string | undefined = undefined;
  let isMouseDown = false;
  let targetTabId: string | undefined = undefined;
  let messagePort: MessagePort | null = null;

  function log(...args: any[]) {
    if (config.debug) {
      console.log('[controlTab]', ...args);
    }
  }

  function error(...args: any[]) {
    console.error('[controlTab]', ...args);
  }

  /**
   * Initializes the control tab with CDP connection to target tab
   * @param browserFullWsEndpoint - WebSocket endpoint for browser connection
   * @param targetId - Target ID to attach to
   * @param port - MessagePort for communication with target tab
   */
  async function init(
    browserFullWsEndpoint: string,
    targetId: string,
    port: MessagePort,
  ): Promise<void> {
    log('Initializing control tab with target:', targetId);

    targetTabId = targetId;
    messagePort = port;

    // Setup message port communication
    setupMessagePortHandlers();

    // Connect to CDP and attach to target
    await connectToCDPAndAttachToTarget(browserFullWsEndpoint, targetId);

    log('Control tab initialized with sessionId:', sessionId);

    // Notify target tab that control tab is ready
    sendResponse('init', { success: true, sessionId });
  }

  /**
   * Sets up message port event handlers for communication with target tab
   */
  function setupMessagePortHandlers(): void {
    if (!messagePort) {
      throw new Error('Message port not available');
    }

    messagePort.onmessage = async (event) => {
      const { id, method, params } = event.data;

      try {
        log('Received command:', method, params);

        let result;
        switch (method) {
          case 'setupBrowserMetrics':
            result = await setupBrowserMetrics(params.viewport);
            break;
          case 'dispatchMouseMove':
            result = await dispatchMouseMove(params.x, params.y);
            break;
          case 'dispatchMouseClick':
            result = await dispatchMouseClick(params.x, params.y, params.button);
            break;
          case 'dispatchMouseDown':
            result = await dispatchMouseDown(params.x, params.y, params.button);
            break;
          case 'dispatchMouseUp':
            result = await dispatchMouseUp(params.x, params.y, params.button);
            break;
          case 'dispatchKeyEvent':
            result = await dispatchKeyEvent(params.type, params.keyData);
            break;
          case 'insertText':
            result = await insertText(params.text);
            break;
          case 'takeScreenshot':
            result = await takeScreenshot();
            break;
          case 'requestNewFrame':
            result = await requestNewFrame();
            break;
          case 'triggerMouseMovement':
            result = await triggerMouseMovement();
            break;
          case 'handleInputEvent':
            result = await handleInputEvent(params.data);
            break;
          default:
            throw new Error(`Unknown method: ${method}`);
        }

        sendResponse(id, { success: true, result });
      } catch (err) {
        error('Error executing command:', method, err);
        sendResponse(id, { success: false, error: (err as Error).message });
      }
    };

    messagePort.start();
    log('Message port handlers setup complete');
  }

  /**
   * Sends response back to target tab via message port
   */
  function sendResponse(id: string, data: any): void {
    if (messagePort) {
      messagePort.postMessage({ id, ...data });
    }
  }

  /**
   * Establishes connection to CDP and attaches to existing target
   */
  async function connectToCDPAndAttachToTarget(
    wsEndpoint: string,
    targetId: string,
  ): Promise<void> {
    try {
      // Create CDP connection
      cdpClient = new CDP({ webSocketDebuggerUrl: wsEndpoint });

      log('Attaching to target:', targetId, wsEndpoint);

      // Attach to the existing page target
      const { sessionId: newSessionId } = await cdpClient.Target.attachToTarget({
        targetId: targetId,
        flatten: true,
      });

      sessionId = newSessionId;

      await cdpClient.Page.enable(undefined, sessionId);
      await cdpClient.Runtime.enable(undefined, sessionId);

      log('✓ Control tab attached to target', targetId, 'with sessionId:', sessionId);
    } catch (err) {
      error('Failed to connect to CDP and attach to target:', err);
      throw err;
    }
  }

  /**
   * Sets up browser window bounds and device metrics
   */
  async function setupBrowserMetrics(viewPort: { width: number; height: number }): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Control tab not initialized');
    }

    try {
      const { windowId } = await cdpClient.Browser.getWindowForTarget({}, sessionId);
      await cdpClient.Browser.setWindowBounds(
        {
          windowId,
          bounds: {
            width: viewPort.width,
            height: viewPort.height + 75,
          },
        },
        sessionId,
      );

      await cdpClient.Emulation.setDeviceMetricsOverride(
        {
          width: viewPort.width,
          height: viewPort.height,
          deviceScaleFactor: 1,
          mobile: false,
        },
        sessionId,
      );

      log(`Browser metrics set to ${viewPort.width}x${viewPort.height}`);
    } catch (err) {
      error('Failed to setup browser metrics:', err);
      throw err;
    }
  }

  /**
   * Dispatches mouse movement event
   */
  async function dispatchMouseMove(x: number, y: number): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Control tab not initialized');
    }

    try {
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mouseMoved',
          x: x,
          y: y,
          buttons: isMouseDown ? 1 : 0,
        },
        sessionId,
      );
      log(`Mouse moved to (${x}, ${y}), isMouseDown: ${isMouseDown}`);
    } catch (err) {
      error('Failed to dispatch mouse move:', err);
      throw err;
    }
  }

  /**
   * Dispatches mouse click event
   */
  async function dispatchMouseClick(
    x: number,
    y: number,
    button: Protocol.Input.MouseButton = 'left',
  ): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Control tab not initialized');
    }

    try {
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mousePressed',
          x: x,
          y: y,
          button: button,
          clickCount: 1,
          buttons: button === 'left' ? 1 : 2,
        },
        sessionId,
      );
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mouseReleased',
          x: x,
          y: y,
          button: button,
          clickCount: 1,
          buttons: 0,
        },
        sessionId,
      );
      log(`Mouse clicked at (${x}, ${y})`);
    } catch (err) {
      error('Failed to dispatch mouse click:', err);
      throw err;
    }
  }

  /**
   * Dispatches mouse down event
   */
  async function dispatchMouseDown(
    x: number,
    y: number,
    button: Protocol.Input.MouseButton = 'left',
  ): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Control tab not initialized');
    }
    try {
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mousePressed',
          x: x,
          y: y,
          button: button,
          clickCount: 1,
          buttons: button === 'left' ? 1 : 2,
        },
        sessionId,
      );
      isMouseDown = true;
      log(`Mouse down at (${x}, ${y})`);
    } catch (err) {
      error('Failed to dispatch mouse down:', err);
      throw err;
    }
  }

  /**
   * Dispatches mouse up event
   */
  async function dispatchMouseUp(
    x: number,
    y: number,
    button: Protocol.Input.MouseButton = 'left',
  ): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Control tab not initialized');
    }
    try {
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mouseReleased',
          x: x,
          y: y,
          button: button,
          clickCount: 1,
          buttons: 0,
        },
        sessionId,
      );
      isMouseDown = false;
      log(`Mouse up at (${x}, ${y})`);
    } catch (err) {
      error('Failed to dispatch mouse up:', err);
      throw err;
    }
  }

  /**
   * Dispatches keyboard event
   */
  async function dispatchKeyEvent(type: string, keyData: any): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Control tab not initialized');
    }
    try {
      const eventData: any = {
        type: type,
        key: keyData.key,
      };

      if (type === 'char') {
        eventData.text = keyData.key;
      } else {
        eventData.code = keyData.code || keyData.key;
        eventData.keyCode = keyData.keyCode || keyData.key.charCodeAt(0);
      }

      // Enhanced special key handling with virtual key codes
      const specialKeyMap: { [key: string]: number } = {
        Backspace: 8,
        Tab: 9,
        Enter: 13,
        Escape: 27,
        Delete: 46,
        ArrowLeft: 37,
        ArrowUp: 38,
        ArrowRight: 39,
        ArrowDown: 40,
        Home: 36,
        End: 35,
        PageUp: 33,
        PageDown: 34,
        Insert: 45,
      };

      if (specialKeyMap[eventData.key]) {
        eventData.windowsVirtualKeyCode = specialKeyMap[eventData.key];
      }

      await cdpClient.Input.dispatchKeyEvent(eventData, sessionId);
    } catch (err) {
      error('Failed to dispatch key event:', err);
      throw err;
    }
  }

  /**
   * Inserts text directly using CDP's insertText method
   */
  async function insertText(text: string): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Control tab not initialized');
    }
    try {
      log('Inserting text directly:', text);
      await cdpClient.Input.insertText({ text: text }, sessionId);
    } catch (err) {
      error('Failed to insert text:', err);
      throw err;
    }
  }

  /**
   * Takes a screenshot using CDP
   */
  async function takeScreenshot(): Promise<string | null> {
    if (!cdpClient || !sessionId) {
      error('takeScreenshot called before control tab initialization.');
      return null;
    }

    try {
      log('Capturing screenshot via CDP...');
      const screenshotResult = await cdpClient.Page.captureScreenshot(
        {
          format: 'png',
          captureBeyondViewport: false,
        },
        sessionId,
      );

      if (!screenshotResult?.data) {
        throw new Error('Page.captureScreenshot did not return data.');
      }

      log('Screenshot captured successfully.');
      return screenshotResult.data;
    } catch (err) {
      error('Error during takeScreenshot:', err);
      return null;
    }
  }

  /**
   * Requests a new frame using invisible opacity changes
   */
  async function requestNewFrame(): Promise<void> {
    if (!cdpClient || !sessionId) {
      error('Control tab not initialized for requestNewFrame');
      return;
    }

    try {
      log('Requesting new frame using invisible opacity technique');

      await cdpClient.Runtime.evaluate(
        {
          expression: `
            (async () => {
              try {
                const body = document.body || document.documentElement;
                const originalOpacity = body.style.opacity;

                body.style.opacity = '0.9999';
                await new Promise(resolve => setTimeout(resolve, 16));
                body.style.opacity = '0.99999';
                await new Promise(resolve => setTimeout(resolve, 16));
                body.style.opacity = originalOpacity || '';

                return { success: true, technique: 'opacity-change', timestamp: Date.now() };
              } catch (e) {
                return { success: false, error: e.message, timestamp: Date.now() };
              }
            })()
          `,
          awaitPromise: true,
          returnByValue: true,
        },
        sessionId,
      );

      log('Completed opacity frame generation technique');
    } catch (err) {
      error('Failed to request new frame via opacity technique:', err);
    }
  }

  /**
   * Triggers a mouse movement to generate new frames
   */
  async function triggerMouseMovement(): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Control tab not initialized');
    }

    try {
      const x = Math.floor(Math.random() * 50) + 50;
      const y = Math.floor(Math.random() * 50) + 50;

      await dispatchMouseMove(x, y);
      log(`Triggered mouse movement to (${x}, ${y}) to generate frames`);
    } catch (err) {
      error('Failed to trigger mouse movement:', err);
      throw err;
    }
  }

  /**
   * Handles input events from target tab (same logic as original browser controller)
   */
  async function handleInputEvent(data: any): Promise<void> {
    try {
      switch (data.type) {
        case 'mousedown':
          await dispatchMouseDown(data.x, data.y, data.button === 0 ? 'left' : 'right');
          break;
        case 'mouseup':
          await dispatchMouseUp(data.x, data.y, data.button === 0 ? 'left' : 'right');
          break;
        case 'mousemove':
          await dispatchMouseMove(data.x, data.y);
          break;
        case 'click':
          await dispatchMouseClick(data.x, data.y, data.button === 0 ? 'left' : 'right');
          break;
        case 'char-input':
          if (data.text !== undefined) {
            if (data.text === '\b' || data.inputType === 'deleteContentBackward') {
              await dispatchKeyEvent('keyDown', { key: 'Backspace' });
              await dispatchKeyEvent('keyUp', { key: 'Backspace' });
            } else if (data.text === '\x7F' || data.inputType === 'deleteContentForward') {
              await dispatchKeyEvent('keyDown', { key: 'Delete' });
              await dispatchKeyEvent('keyUp', { key: 'Delete' });
            } else if (
              data.text === '\n' ||
              data.inputType === 'insertLineBreak' ||
              data.inputType === 'insertParagraph'
            ) {
              await dispatchKeyEvent('keyDown', { key: 'Enter' });
              await dispatchKeyEvent('keyUp', { key: 'Enter' });
            } else if (data.text && data.text.length > 0) {
              await insertText(data.text);
            }
          }
          break;
        case 'text-insert':
          if (data.text && data.text.length > 0) {
            const sanitizedText = data.text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
            if (sanitizedText.length > 0) {
              await insertText(sanitizedText);
            }
          }
          break;
        case 'navigation-key':
          await dispatchKeyEvent('keyDown', data);
          await dispatchKeyEvent('keyUp', data);
          break;
        case 'keydown':
          await dispatchKeyEvent('keyDown', data);
          break;
        case 'keyup':
          await dispatchKeyEvent('keyUp', data);
          break;
        case 'keypress':
          await dispatchKeyEvent('char', data);
          break;
        default:
          log('Unhandled input type:', data.type);
      }
    } catch (err) {
      error('Failed to handle input event:', err);
    }
  }

  /**
   * Handles initialization message from tab coordinator
   */
  function handleInitMessage(event: MessageEvent): void {
    const data = event.data as any;
    if (data && data.type === 'INIT_CONTROL_TAB') {
      const { browserWsEndpoint, targetId } = data;
      const ports = (event as any).ports;
      const port = ports && ports[0];

      if (port) {
        log('Received initialization message from tab coordinator');
        init(browserWsEndpoint, targetId, port)
          .then(() => {
            // Notify tab coordinator that control tab is ready
            (globalThis as any).window.parent.postMessage({ type: 'CONTROL_TAB_READY' }, '*');
          })
          .catch((err) => {
            error('Failed to initialize control tab:', err);
          });
      }
    }
  }

  // Listen for initialization message from tab coordinator
  (globalThis as any).window.addEventListener('message', handleInitMessage);

  // Expose public API
  (globalThis as any).controlTab = {
    init,
  };

  log('Control tab script loaded and ready for initialization');
})();
