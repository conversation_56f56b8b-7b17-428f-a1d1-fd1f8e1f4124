import Protocol from 'devtools-protocol';
import { CDP } from '../browser/simple-cdp';

declare global {
  interface Window {
    controlTab: any;
  }
}

/**
 * Control Tab - Isolated CDP Controller
 *
 * This script runs in a separate browser tab and manages all CDP operations
 * for the target tab. It receives commands via message passing and executes
 * CDP operations, providing isolation from target page reloads.
 */
(function () {
  const config = {
    debug: true,
  };

  let cdpClient: CDP | null = null;
  let sessionId: string | undefined = undefined;
  let isMouseDown = false;
  let targetTabId: string | undefined = undefined;
  let messagePort: MessagePort | null = null;

  function log(...args: any[]) {
    if (config.debug) {
      console.log('[controlTab]', ...args);
    }
  }

  function error(...args: any[]) {
    console.error('[controlTab]', ...args);
  }

  /**
   * Initializes the control tab with CDP connection to target tab
   * @param browserFullWsEndpoint - WebSocket endpoint for browser connection
   * @param targetId - Target ID to attach to
   * @param port - MessagePort for communication with target tab
   */
  async function init(
    browserFullWsEndpoint: string,
    targetId: string,
    port: MessagePort,
  ): Promise<void> {
    log('Initializing control tab with target:', targetId);

    targetTabId = targetId;
    messagePort = port;

    // Setup message port communication
    setupMessagePortHandlers();

    // Connect to CDP and attach to target
    await connectToCDPAndAttachToTarget(browserFullWsEndpoint, targetId);

    log('Control tab initialized with sessionId:', sessionId);

    // Notify target tab that control tab is ready
    sendResponse('init', { success: true, sessionId });
  }

  /**
   * Sets up message port event handlers for communication with target tab
   */
  function setupMessagePortHandlers(): void {
    if (!messagePort) {
      throw new Error('Message port not available');
    }

    messagePort.onmessage = async (event) => {
      const { id, method, params } = event.data;

      try {
        log('Received command:', method, params);

        let result;
        switch (method) {
          case 'setupBrowserMetrics':
            result = await setupBrowserMetrics(params.viewport);
            break;
          case 'dispatchMouseMove':
            result = await dispatchMouseMove(params.x, params.y);
            break;
          case 'dispatchMouseClick':
            result = await dispatchMouseClick(params.x, params.y, params.button);
            break;
          case 'dispatchMouseDown':
            result = await dispatchMouseDown(params.x, params.y, params.button);
            break;
          case 'dispatchMouseUp':
            result = await dispatchMouseUp(params.x, params.y, params.button);
            break;
          case 'dispatchKeyEvent':
            result = await dispatchKeyEvent(params.type, params.keyData);
            break;
          case 'insertText':
            result = await insertText(params.text);
            break;
          case 'takeScreenshot':
            result = await takeScreenshot();
            break;
          case 'requestNewFrame':
            result = await requestNewFrame();
            break;
          case 'triggerMouseMovement':
            result = await triggerMouseMovement();
            break;
          case 'handleInputEvent':
            result = await handleInputEvent(params.data);
            break;
          default:
            throw new Error(`Unknown method: ${method}`);
        }

        sendResponse(id, { success: true, result });
      } catch (err) {
        error('Error executing command:', method, err);
        sendResponse(id, { success: false, error: (err as Error).message });
      }
    };

    messagePort.start();
    log('Message port handlers setup complete');
  }

  /**
   * Sends response back to target tab via message port
   */
  function sendResponse(id: string, data: any): void {
    if (messagePort) {
      messagePort.postMessage({ id, ...data });
    }
  }

  /**
   * Establishes connection to CDP and attaches to existing target
   */
  async function connectToCDPAndAttachToTarget(
    wsEndpoint: string,
    targetId: string,
  ): Promise<void> {
    try {
      // Create CDP connection
      cdpClient = new CDP({ webSocketDebuggerUrl: wsEndpoint });

      log('Attaching to target:', targetId, wsEndpoint);

      // Attach to the existing page target
      const { sessionId: newSessionId } = await cdpClient.Target.attachToTarget({
        targetId: targetId,
        flatten: true,
      });

      sessionId = newSessionId;

      await cdpClient.Page.enable(undefined, sessionId);
      await cdpClient.Runtime.enable(undefined, sessionId);

      log('✓ Control tab attached to target', targetId, 'with sessionId:', sessionId);
    } catch (err) {
      error('Failed to connect to CDP and attach to target:', err);
      throw err;
    }
  }

  /**
   * Sets up browser window bounds and device metrics
   */
  async function setupBrowserMetrics(viewPort: { width: number; height: number }): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Control tab not initialized');
    }

    try {
      const { windowId } = await cdpClient.Browser.getWindowForTarget({}, sessionId);
      await cdpClient.Browser.setWindowBounds(
        {
          windowId,
          bounds: {
            width: viewPort.width,
            height: viewPort.height + 75,
          },
        },
        sessionId,
      );

      await cdpClient.Emulation.setDeviceMetricsOverride(
        {
          width: viewPort.width,
          height: viewPort.height,
          deviceScaleFactor: 1,
          mobile: false,
        },
        sessionId,
      );

      log(`Browser metrics set to ${viewPort.width}x${viewPort.height}`);
    } catch (err) {
      error('Failed to setup browser metrics:', err);
      throw err;
    }
  }

  /**
   * Dispatches mouse movement event
   */
  async function dispatchMouseMove(x: number, y: number): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Control tab not initialized');
    }

    try {
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mouseMoved',
          x: x,
          y: y,
          buttons: isMouseDown ? 1 : 0,
        },
        sessionId,
      );
      log(`Mouse moved to (${x}, ${y}), isMouseDown: ${isMouseDown}`);
    } catch (err) {
      error('Failed to dispatch mouse move:', err);
      throw err;
    }
  }

  /**
   * Dispatches mouse click event
   */
  async function dispatchMouseClick(
    x: number,
    y: number,
    button: Protocol.Input.MouseButton = 'left',
  ): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Control tab not initialized');
    }

    try {
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mousePressed',
          x: x,
          y: y,
          button: button,
          clickCount: 1,
          buttons: button === 'left' ? 1 : 2,
        },
        sessionId,
      );
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mouseReleased',
          x: x,
          y: y,
          button: button,
          clickCount: 1,
          buttons: 0,
        },
        sessionId,
      );
      log(`Mouse clicked at (${x}, ${y})`);
    } catch (err) {
      error('Failed to dispatch mouse click:', err);
      throw err;
    }
  }

  /**
   * Dispatches mouse down event
   */
  async function dispatchMouseDown(
    x: number,
    y: number,
    button: Protocol.Input.MouseButton = 'left',
  ): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Control tab not initialized');
    }
    try {
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mousePressed',
          x: x,
          y: y,
          button: button,
          clickCount: 1,
          buttons: button === 'left' ? 1 : 2,
        },
        sessionId,
      );
      isMouseDown = true;
      log(`Mouse down at (${x}, ${y})`);
    } catch (err) {
      error('Failed to dispatch mouse down:', err);
      throw err;
    }
  }

  /**
   * Dispatches mouse up event
   */
  async function dispatchMouseUp(
    x: number,
    y: number,
    button: Protocol.Input.MouseButton = 'left',
  ): Promise<void> {
    if (!cdpClient || !sessionId) {
      throw new Error('Control tab not initialized');
    }
    try {
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mouseReleased',
          x: x,
          y: y,
          button: button,
          clickCount: 1,
          buttons: 0,
        },
        sessionId,
      );
      isMouseDown = false;
      log(`Mouse up at (${x}, ${y})`);
    } catch (err) {
      error('Failed to dispatch mouse up:', err);
      throw err;
    }
  }

  // Expose public API
  (globalThis as any).controlTab = {
    init,
  };
})();
