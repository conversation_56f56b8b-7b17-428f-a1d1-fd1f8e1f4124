declare global {
  interface Window {
    browserController: any;
  }
}

/**
 * Target Tab Client API
 * 
 * This script runs in the target tab and provides the same API as the original
 * browser-controller but uses message passing to communicate with the control tab
 * instead of direct CDP operations. This maintains API compatibility while
 * providing isolation from page reloads.
 */
(function () {
  const config = {
    debug: true,
  };

  let messagePort: MessagePort | null = null;
  let isInitialized = false;
  let pendingRequests = new Map<string, { resolve: Function; reject: Function }>();
  let requestIdCounter = 0;

  function log(...args: any[]) {
    if (config.debug) {
      console.log('[targetTabClient]', ...args);
    }
  }

  function error(...args: any[]) {
    console.error('[targetTabClient]', ...args);
  }

  /**
   * Generates a unique request ID
   */
  function generateRequestId(): string {
    return `req_${Date.now()}_${++requestIdCounter}`;
  }

  /**
   * Sends a command to the control tab and returns a promise that resolves with the result
   */
  function sendCommand(method: string, params: any = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!messagePort) {
        reject(new Error('Message port not available'));
        return;
      }

      const id = generateRequestId();
      pendingRequests.set(id, { resolve, reject });

      log('Sending command:', method, params);
      messagePort.postMessage({ id, method, params });

      // Set timeout for request
      setTimeout(() => {
        if (pendingRequests.has(id)) {
          pendingRequests.delete(id);
          reject(new Error(`Request timeout for method: ${method}`));
        }
      }, 30000); // 30 second timeout
    });
  }

  /**
   * Sets up message port communication with control tab
   */
  function setupMessagePortHandlers(port: MessagePort): void {
    messagePort = port;

    messagePort.onmessage = (event) => {
      const { id, success, result, error: errorMsg } = event.data;

      if (id === 'init') {
        // Handle initialization response
        if (success) {
          isInitialized = true;
          log('Target tab client initialized successfully');
        } else {
          error('Failed to initialize target tab client:', errorMsg);
        }
        return;
      }

      // Handle command responses
      const pendingRequest = pendingRequests.get(id);
      if (pendingRequest) {
        pendingRequests.delete(id);
        
        if (success) {
          pendingRequest.resolve(result);
        } else {
          pendingRequest.reject(new Error(errorMsg || 'Unknown error'));
        }
      }
    };

    messagePort.start();
    log('Message port handlers setup complete');
  }

  /**
   * Initializes the target tab client with message port from control tab
   * This replaces the original init method that created CDP connections
   */
  async function init(port: MessagePort): Promise<void> {
    log('Initializing target tab client...');
    
    setupMessagePortHandlers(port);
    
    // Wait for initialization confirmation from control tab
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Initialization timeout'));
      }, 10000);

      const checkInitialized = () => {
        if (isInitialized) {
          clearTimeout(timeout);
          resolve();
        } else {
          setTimeout(checkInitialized, 100);
        }
      };

      checkInitialized();
    });
  }

  /**
   * Sets up browser window bounds and device metrics
   */
  async function setupBrowserMetrics(viewport: { width: number; height: number }): Promise<void> {
    return sendCommand('setupBrowserMetrics', { viewport });
  }

  /**
   * Dispatches mouse movement event
   */
  async function dispatchMouseMove(x: number, y: number): Promise<void> {
    return sendCommand('dispatchMouseMove', { x, y });
  }

  /**
   * Dispatches mouse click event
   */
  async function dispatchMouseClick(x: number, y: number, button: string = 'left'): Promise<void> {
    return sendCommand('dispatchMouseClick', { x, y, button });
  }

  /**
   * Dispatches mouse down event
   */
  async function dispatchMouseDown(x: number, y: number, button: string = 'left'): Promise<void> {
    return sendCommand('dispatchMouseDown', { x, y, button });
  }

  /**
   * Dispatches mouse up event
   */
  async function dispatchMouseUp(x: number, y: number, button: string = 'left'): Promise<void> {
    return sendCommand('dispatchMouseUp', { x, y, button });
  }

  /**
   * Dispatches keyboard event
   */
  async function dispatchKeyEvent(type: string, keyData: any): Promise<void> {
    return sendCommand('dispatchKeyEvent', { type, keyData });
  }

  /**
   * Inserts text directly
   */
  async function insertText(text: string): Promise<void> {
    return sendCommand('insertText', { text });
  }

  /**
   * Takes a screenshot
   */
  async function takeScreenshot(): Promise<string | null> {
    return sendCommand('takeScreenshot');
  }

  /**
   * Requests a new frame
   */
  async function requestNewFrame(): Promise<void> {
    return sendCommand('requestNewFrame');
  }

  /**
   * Triggers mouse movement to generate frames
   */
  async function triggerMouseMovement(): Promise<void> {
    return sendCommand('triggerMouseMovement');
  }

  /**
   * Handles input events (same interface as original)
   */
  async function handleInputEvent(data: any): Promise<void> {
    return sendCommand('handleInputEvent', { data });
  }

  // Expose the same API as the original browserController
  (globalThis as any).browserController = {
    init,
    setupBrowserMetrics,
    dispatchMouseMove,
    dispatchMouseDown,
    dispatchMouseUp,
    dispatchMouseClick,
    dispatchKeyEvent,
    insertText,
    takeScreenshot,
    handleInputEvent,
    requestNewFrame,
    triggerMouseMovement,
  };

  log('Target tab client API exposed as window.browserController');
})();
