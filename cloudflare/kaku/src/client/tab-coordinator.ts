/**
 * Tab Coordinator - Message Passing System
 *
 * This module manages the creation and coordination of the control tab and target tab,
 * establishing the message passing communication between them using MessageChannel.
 */

interface TabCoordinatorConfig {
  browserWsEndpoint: string;
  targetId: string;
  debug?: boolean;
}

interface TabInfo {
  window: any; // Use any to avoid TypeScript issues with Window properties
  ready: boolean;
}

export class TabCoordinator {
  private config: TabCoordinatorConfig;
  private controlTab: TabInfo | null = null;
  private targetTab: TabInfo | null = null;
  private messageChannel: any = null; // Use any for MessageChannel
  private debug: boolean;

  constructor(config: TabCoordinatorConfig) {
    this.config = config;
    this.debug = config.debug ?? true;
  }

  private log(...args: any[]) {
    if (this.debug) {
      console.log('[tabCoordinator]', ...args);
    }
  }

  private error(...args: any[]) {
    console.error('[tabCoordinator]', ...args);
  }

  /**
   * Initializes the two-tab architecture
   */
  async init(): Promise<void> {
    this.log('Initializing two-tab architecture...');

    try {
      // Step 1: Create control tab
      await this.createControlTab();

      // Step 2: Setup target tab (current tab)
      this.setupTargetTab();

      // Step 3: Establish message passing
      await this.establishMessagePassing();

      this.log('Two-tab architecture initialized successfully');
    } catch (err) {
      this.error('Failed to initialize two-tab architecture:', err);
      throw err;
    }
  }

  /**
   * Creates the control tab in a new browser tab
   */
  private async createControlTab(): Promise<void> {
    this.log('Creating control tab...');

    // Create a new tab for the control tab
    const controlTabWindow = (globalThis as any).window.open('about:blank', '_blank');

    if (!controlTabWindow) {
      throw new Error('Failed to create control tab - popup blocked?');
    }

    this.controlTab = {
      window: controlTabWindow,
      ready: false,
    };

    // Wait for the control tab to load
    await this.waitForTabLoad(controlTabWindow);

    // Inject the control tab script
    await this.injectControlTabScript(controlTabWindow);

    this.log('Control tab created and script injected');
  }

  /**
   * Sets up the target tab (current tab)
   */
  private setupTargetTab(): void {
    this.log('Setting up target tab...');

    this.targetTab = {
      window: (globalThis as any).window,
      ready: false,
    };

    this.log('Target tab setup complete');
  }

  /**
   * Establishes message passing between control tab and target tab
   */
  private async establishMessagePassing(): Promise<void> {
    this.log('Establishing message passing...');

    if (!this.controlTab || !this.targetTab) {
      throw new Error('Both tabs must be created before establishing message passing');
    }

    // Create MessageChannel for communication
    this.messageChannel = new MessageChannel();
    const { port1, port2 } = this.messageChannel;

    // port1 goes to control tab, port2 stays in target tab

    // Initialize control tab with its port
    await this.initializeControlTab(port1);

    // Initialize target tab with its port
    await this.initializeTargetTab(port2);

    this.log('Message passing established successfully');
  }

  /**
   * Waits for a tab to finish loading
   */
  private waitForTabLoad(tabWindow: Window): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Tab load timeout'));
      }, 10000);

      const checkLoad = () => {
        try {
          if ((tabWindow as any).document.readyState === 'complete') {
            clearTimeout(timeout);
            resolve();
          } else {
            setTimeout(checkLoad, 100);
          }
        } catch (err) {
          // Tab might not be accessible yet
          setTimeout(checkLoad, 100);
        }
      };

      checkLoad();
    });
  }

  /**
   * Injects the control tab script into the control tab window
   */
  private async injectControlTabScript(tabWindow: Window): Promise<void> {
    this.log('Injecting control tab script...');

    // Get the control tab script content
    const scriptUrl = this.getControlTabScriptUrl();
    const scriptContent = await fetch(scriptUrl).then((res) => res.text());

    // Create and inject script element
    const script = (tabWindow as any).document.createElement('script');
    script.textContent = scriptContent;
    (tabWindow as any).document.head.appendChild(script);

    this.log('Control tab script injected successfully');
  }

  /**
   * Gets the URL for the control tab script
   */
  private getControlTabScriptUrl(): string {
    // This would be the built/minified version of control-tab.ts
    // For now, we'll use a placeholder - this needs to be updated based on your build system
    return '/scripts/control-tab.min.js';
  }

  /**
   * Initializes the control tab with CDP connection and message port
   */
  private async initializeControlTab(port: MessagePort): Promise<void> {
    this.log('Initializing control tab...');

    if (!this.controlTab) {
      throw new Error('Control tab not created');
    }

    // Transfer the port to the control tab
    this.controlTab.window.postMessage(
      {
        type: 'INIT_CONTROL_TAB',
        browserWsEndpoint: this.config.browserWsEndpoint,
        targetId: this.config.targetId,
      },
      '*',
      [port],
    );

    // Wait for control tab to be ready
    await this.waitForControlTabReady();

    this.controlTab.ready = true;
    this.log('Control tab initialized successfully');
  }

  /**
   * Initializes the target tab with the client API and message port
   */
  private async initializeTargetTab(port: MessagePort): Promise<void> {
    this.log('Initializing target tab...');

    if (!this.targetTab) {
      throw new Error('Target tab not created');
    }

    // Initialize the target tab client with the message port
    if ((window as any).browserController && (window as any).browserController.init) {
      await (window as any).browserController.init(port);
      this.targetTab.ready = true;
      this.log('Target tab initialized successfully');
    } else {
      throw new Error('Target tab client API not available');
    }
  }

  /**
   * Waits for the control tab to signal it's ready
   */
  private waitForControlTabReady(): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Control tab initialization timeout'));
      }, 15000);

      const messageHandler = (event: MessageEvent) => {
        if (event.data.type === 'CONTROL_TAB_READY') {
          clearTimeout(timeout);
          (globalThis as any).window.removeEventListener('message', messageHandler);
          resolve();
        }
      };

      (globalThis as any).window.addEventListener('message', messageHandler);
    });
  }

  /**
   * Checks if the two-tab system is ready
   */
  isReady(): boolean {
    return !!(this.controlTab?.ready && this.targetTab?.ready && this.messageChannel);
  }

  /**
   * Monitors control tab health and attempts recovery if needed
   */
  private startHealthMonitoring(): void {
    const healthCheckInterval = setInterval(() => {
      if (!this.isControlTabHealthy()) {
        this.log('Control tab health check failed, attempting recovery...');
        this.recoverControlTab()
          .then(() => {
            this.log('Control tab recovery successful');
          })
          .catch((err) => {
            this.error('Control tab recovery failed:', err);
          });
      }
    }, 5000); // Check every 5 seconds

    // Store interval ID for cleanup
    (this as any).healthCheckInterval = healthCheckInterval;
  }

  /**
   * Checks if the control tab is still healthy
   */
  private isControlTabHealthy(): boolean {
    if (!this.controlTab?.window) {
      return false;
    }

    try {
      // Try to access the control tab window
      return !this.controlTab.window.closed;
    } catch (err) {
      // If we can't access it, it's probably closed or crashed
      return false;
    }
  }

  /**
   * Attempts to recover the control tab
   */
  private async recoverControlTab(): Promise<void> {
    this.log('Attempting to recover control tab...');

    try {
      // Close the old control tab if it exists
      if (this.controlTab?.window && !this.controlTab.window.closed) {
        this.controlTab.window.close();
      }

      // Create a new control tab
      await this.createControlTab();

      // Re-establish message passing
      if (this.messageChannel) {
        const { port1 } = this.messageChannel;
        await this.initializeControlTab(port1);
      }

      this.log('Control tab recovery completed successfully');
    } catch (err) {
      this.error('Failed to recover control tab:', err);
      throw err;
    }
  }

  /**
   * Enhanced cleanup with health monitoring cleanup
   */
  cleanup(): void {
    this.log('Cleaning up tab coordinator...');

    // Clear health monitoring
    if ((this as any).healthCheckInterval) {
      clearInterval((this as any).healthCheckInterval);
    }

    if (this.controlTab && this.controlTab.window) {
      this.controlTab.window.close();
    }

    if (this.messageChannel) {
      // Close message channel ports if needed
    }

    this.controlTab = null;
    this.targetTab = null;
    this.messageChannel = null;

    this.log('Tab coordinator cleanup complete');
  }
}
