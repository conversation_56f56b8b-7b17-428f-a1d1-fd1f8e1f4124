import { TabCoordinator } from './tab-coordinator';

declare global {
  interface Window {
    browserController: any;
  }
}

/**
 * Browser Controller V2 - Two-Tab Architecture
 * 
 * This is the new browser controller that uses the two-tab architecture
 * to resolve browser crashes on page reloads. It maintains the same API
 * as the original browser controller for backward compatibility.
 */
(function () {
  const config = {
    debug: true,
  };

  let tabCoordinator: TabCoordinator | null = null;
  let isInitialized = false;

  function log(...args: any[]) {
    if (config.debug) {
      console.log('[browserControllerV2]', ...args);
    }
  }

  function error(...args: any[]) {
    console.error('[browserControllerV2]', ...args);
  }

  /**
   * Initializes the two-tab browser controller architecture
   * @param browserFullWsEndpoint - WebSocket endpoint for browser connection
   * @param targetId - Target ID to attach to
   */
  async function init(browserFullWsEndpoint: string, targetId: string): Promise<void> {
    log('Initializing two-tab browser controller...');
    
    try {
      // Create tab coordinator
      tabCoordinator = new TabCoordinator({
        browserWsEndpoint: browserFullWsEndpoint,
        targetId: targetId,
        debug: config.debug,
      });

      // Initialize the two-tab system
      await tabCoordinator.init();

      isInitialized = true;
      log('Two-tab browser controller initialized successfully');
    } catch (err) {
      error('Failed to initialize two-tab browser controller:', err);
      throw err;
    }
  }

  /**
   * Ensures the browser controller is initialized before operations
   */
  function ensureInitialized(): void {
    if (!isInitialized || !tabCoordinator || !tabCoordinator.isReady()) {
      throw new Error('Browser controller not initialized or not ready');
    }
  }

  /**
   * Sets up browser window bounds and device metrics
   * @param viewPort - Viewport dimensions
   */
  async function setupBrowserMetrics(viewPort: { width: number; height: number }): Promise<void> {
    ensureInitialized();
    
    // The target tab client will handle this via message passing
    if ((window as any).browserController && (window as any).browserController.setupBrowserMetrics) {
      return (window as any).browserController.setupBrowserMetrics(viewPort);
    }
    
    throw new Error('Target tab client not available');
  }

  /**
   * Dispatches mouse movement event
   */
  async function dispatchMouseMove(x: number, y: number): Promise<void> {
    ensureInitialized();
    
    if ((window as any).browserController && (window as any).browserController.dispatchMouseMove) {
      return (window as any).browserController.dispatchMouseMove(x, y);
    }
    
    throw new Error('Target tab client not available');
  }

  /**
   * Dispatches mouse click event
   */
  async function dispatchMouseClick(
    x: number,
    y: number,
    button: string = 'left',
  ): Promise<void> {
    ensureInitialized();
    
    if ((window as any).browserController && (window as any).browserController.dispatchMouseClick) {
      return (window as any).browserController.dispatchMouseClick(x, y, button);
    }
    
    throw new Error('Target tab client not available');
  }

  /**
   * Dispatches mouse down event
   */
  async function dispatchMouseDown(
    x: number,
    y: number,
    button: string = 'left',
  ): Promise<void> {
    ensureInitialized();
    
    if ((window as any).browserController && (window as any).browserController.dispatchMouseDown) {
      return (window as any).browserController.dispatchMouseDown(x, y, button);
    }
    
    throw new Error('Target tab client not available');
  }

  /**
   * Dispatches mouse up event
   */
  async function dispatchMouseUp(
    x: number,
    y: number,
    button: string = 'left',
  ): Promise<void> {
    ensureInitialized();
    
    if ((window as any).browserController && (window as any).browserController.dispatchMouseUp) {
      return (window as any).browserController.dispatchMouseUp(x, y, button);
    }
    
    throw new Error('Target tab client not available');
  }

  /**
   * Dispatches keyboard event
   */
  async function dispatchKeyEvent(type: string, keyData: any): Promise<void> {
    ensureInitialized();
    
    if ((window as any).browserController && (window as any).browserController.dispatchKeyEvent) {
      return (window as any).browserController.dispatchKeyEvent(type, keyData);
    }
    
    throw new Error('Target tab client not available');
  }

  /**
   * Inserts text directly
   */
  async function insertText(text: string): Promise<void> {
    ensureInitialized();
    
    if ((window as any).browserController && (window as any).browserController.insertText) {
      return (window as any).browserController.insertText(text);
    }
    
    throw new Error('Target tab client not available');
  }

  /**
   * Takes a screenshot
   */
  async function takeScreenshot(): Promise<string | null> {
    ensureInitialized();
    
    if ((window as any).browserController && (window as any).browserController.takeScreenshot) {
      return (window as any).browserController.takeScreenshot();
    }
    
    throw new Error('Target tab client not available');
  }

  /**
   * Handles input events
   */
  async function handleInputEvent(data: any): Promise<void> {
    ensureInitialized();
    
    if ((window as any).browserController && (window as any).browserController.handleInputEvent) {
      return (window as any).browserController.handleInputEvent(data);
    }
    
    throw new Error('Target tab client not available');
  }

  /**
   * Requests a new frame
   */
  async function requestNewFrame(): Promise<void> {
    ensureInitialized();
    
    if ((window as any).browserController && (window as any).browserController.requestNewFrame) {
      return (window as any).browserController.requestNewFrame();
    }
    
    throw new Error('Target tab client not available');
  }

  /**
   * Triggers mouse movement to generate frames
   */
  async function triggerMouseMovement(): Promise<void> {
    ensureInitialized();
    
    if ((window as any).browserController && (window as any).browserController.triggerMouseMovement) {
      return (window as any).browserController.triggerMouseMovement();
    }
    
    throw new Error('Target tab client not available');
  }

  /**
   * Cleanup function to properly close tabs and connections
   */
  function cleanup(): void {
    log('Cleaning up browser controller...');
    
    if (tabCoordinator) {
      tabCoordinator.cleanup();
      tabCoordinator = null;
    }
    
    isInitialized = false;
    log('Browser controller cleanup complete');
  }

  // Expose the same API as the original browser controller
  (globalThis as any).browserController = {
    init,
    setupBrowserMetrics,
    dispatchMouseMove,
    dispatchMouseDown,
    dispatchMouseUp,
    dispatchMouseClick,
    dispatchKeyEvent,
    insertText,
    takeScreenshot,
    handleInputEvent,
    requestNewFrame,
    triggerMouseMovement,
    cleanup, // New method for proper cleanup
  };

  log('Browser Controller V2 (Two-Tab Architecture) loaded');
})();
